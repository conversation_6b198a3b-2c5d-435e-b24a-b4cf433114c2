<html lang="en" class="dark">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Pak Funding - Sign in</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="f_SW50ZXI">
    <div class="min-h-screen flex items-center justify-center bg-white">
        <div class="rounded-lg border w-full max-w-md bg-white text-gray-900 border-gray-200 shadow-xl" data-v0-t="card">
            <div class="flex flex-col p-6 space-y-1 text-center pb-6">
                <div class="flex items-center justify-center space-x-2 mb-4">
                    <div
                        class="w-10 h-10 bg-[#1a4d3a] rounded-md flex items-center justify-center text-white font-bold text-lg shadow-lg">
                        P</div><span class="text-xl font-semibold text-gray-900">Pak Funding</span>
                </div>
                <h3 class="tracking-tight text-2xl font-bold text-gray-900">Welcome Back</h3>
                <p class="text-sm text-gray-600">Sign in to your trader account</p>
            </div>
            <div class="p-6 pt-0">
                <form class="space-y-4">
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-900 font-medium"
                            for="email">Email</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-gray-800 focus:ring-gray-800"
                            id="email" placeholder="<EMAIL>" required="" type="email"
                            value=""></div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-900 font-medium"
                            for="password">Password</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-gray-800 focus:ring-gray-800"
                            id="password" placeholder="••••••••" required="" type="password" value=""></div>
                    <button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 w-full bg-[#1a4d3a] text-white hover:bg-[#1a4d3a]/90 font-medium py-2.5"
                        type="submit">Sign In</button>
                </form>
                <div class="mt-4 text-center text-sm text-gray-600">Don't have an account? <a href="/auth/signup"
                        class="text-gray-800 hover:underline font-medium">Sign up</a></div>
                <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h3 class="font-semibold mb-2 text-gray-900">Demo Credentials:</h3>
                    <p class="text-sm text-gray-700">Email: <span class="font-medium"><EMAIL></span></p>
                    <p class="text-sm text-gray-700">Password: <span class="font-medium">demo</span></p>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
