<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PayoutRequestsController extends Controller
{
    public function index()
    {
        return view('payouts');
    }

    /**
     * Get list of payout requests for API
     *
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        try {
            // TODO: Replace with actual database query
            // For now, return mock data that matches the frontend
            $payouts = [
                [
                    'id' => 1,
                    'amount' => 5000.00,
                    'method' => 'bank_transfer',
                    'method_display' => 'Bank Transfer',
                    'status' => 'completed',
                    'requested_at' => '2024-01-15',
                    'completed_at' => '2024-01-18',
                    'notes' => 'First payout completed successfully'
                ],
                [
                    'id' => 2,
                    'amount' => 3000.00,
                    'method' => 'paypal',
                    'method_display' => 'PayPal',
                    'status' => 'pending',
                    'requested_at' => '2024-01-20',
                    'completed_at' => null,
                    'notes' => 'Awaiting approval'
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $payouts
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payout requests', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching payout requests.'
            ], 500);
        }
    }

    /**
     * Store a new payout request
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Validate the request data
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1|max:100000',
                'method' => 'required|string|in:bank_transfer,paypal,crypto',
                'notes' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validatedData = $validator->validated();

            // Here you would typically save to database
            // For now, we'll just log the request and return success
            Log::info('Payout request received', [
                'amount' => $validatedData['amount'],
                'method' => $validatedData['method'],
                'notes' => $validatedData['notes'] ?? null,
                // 'user_id' => auth()->id() ?? 'guest', // If you have authentication
                'timestamp' => now()
            ]);

            // TODO: Save to database
            // $payout = PayoutRequest::create([
            //     'user_id' => auth()->id(),
            //     'amount' => $validatedData['amount'],
            //     'method' => $validatedData['method'],
            //     'notes' => $validatedData['notes'],
            //     'status' => 'pending'
            // ]);

            return response()->json([
                'success' => true,
                'message' => 'Payout request submitted successfully',
                'data' => [
                    'amount' => $validatedData['amount'],
                    'method' => $validatedData['method'],
                    'status' => 'pending',
                    'submitted_at' => now()->toISOString()
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error processing payout request', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request. Please try again.'
            ], 500);
        }
    }
}
