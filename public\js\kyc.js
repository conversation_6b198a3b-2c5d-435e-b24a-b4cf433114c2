// KYC page JavaScript functionality
document.addEventListener('DOMContentLoaded', function () {
    console.log('KYC page loaded');

    // Initialize KYC functionality
    initializeFileUploads();
    initializeDocumentCards();
});

/**
 * Initialize file upload functionality
 */
function initializeFileUploads() {
    // Map buttons to their corresponding file inputs and document types using specific IDs
    const documentMappings = [
        {
            type: 'cnic_front',
            button: document.getElementById('cnic_front_btn'),
            input: document.getElementById('cnic_front_input')
        },
        {
            type: 'cnic_back',
            button: document.getElementById('cnic_back_btn'),
            input: document.getElementById('cnic_back_input')
        },
        {
            type: 'selfie_with_cnic',
            button: document.getElementById('selfie_btn'),
            input: document.getElementById('selfie_input')
        }
    ];

    documentMappings.forEach(mapping => {
        if (mapping.button && mapping.input) {
            // Click button triggers file input
            mapping.button.addEventListener('click', function(e) {
                e.preventDefault();
                mapping.input.click();
            });

            // Handle file selection
            mapping.input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleFileUpload(file, mapping.type, mapping.button);
                }
            });
        }
    });
}

/**
 * Initialize document card interactions
 */
function initializeDocumentCards() {
    const documentCards = document.querySelectorAll('[data-v0-t="card"]');
    
    documentCards.forEach((card, index) => {
        // Skip the guidelines card (last one)
        if (index < 3) {
            card.addEventListener('click', function(e) {
                // Only trigger if not clicking on a button
                if (!e.target.closest('button')) {
                    const button = card.querySelector('button');
                    if (button && !button.disabled) {
                        button.click();
                    }
                }
            });
        }
    });
}

/**
 * Handle file upload process
 */
function handleFileUpload(file, documentType, button) {
    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
        showNotification(validation.message, 'error');
        return;
    }

    // Show loading state
    const originalText = button.textContent;
    button.textContent = 'Uploading...';
    button.disabled = true;

    // Create FormData
    const formData = new FormData();
    formData.append('document', file);
    formData.append('type', documentType);

    // Upload file
    fetch('/api/kyc/upload', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => Promise.reject(err));
        }
        return response.json();
    })
    .then(data => {
        console.log('Upload success:', data);
        showNotification(data.message || 'Document uploaded successfully!', 'success');
        
        // Update the UI to reflect the upload
        updateDocumentStatus(documentType, 'pending', data.data);
        
        // Reset button
        button.textContent = 'Replace Document';
        button.disabled = false;
    })
    .catch(error => {
        console.error('Upload error:', error);
        
        // Reset button state
        button.textContent = originalText;
        button.disabled = false;

        // Show error message
        let errorMessage = 'Error uploading document. Please try again.';
        if (error.errors) {
            const firstError = Object.values(error.errors)[0];
            if (firstError && firstError.length > 0) {
                errorMessage = firstError[0];
            }
        } else if (error.message) {
            errorMessage = error.message;
        }

        showNotification(errorMessage, 'error');
    });
}

/**
 * Validate uploaded file
 */
function validateFile(file) {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];

    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            message: 'Invalid file type. Please upload JPG, PNG, or PDF files only.'
        };
    }

    if (file.size > maxSize) {
        return {
            valid: false,
            message: 'File size too large. Please upload files smaller than 5MB.'
        };
    }

    return { valid: true };
}

/**
 * Update document status in the UI
 */
function updateDocumentStatus(documentType, status, data) {
    const documentCards = document.querySelectorAll('[data-v0-t="card"]');
    let cardIndex;

    // Map document type to card index
    switch(documentType) {
        case 'cnic_front':
            cardIndex = 0;
            break;
        case 'cnic_back':
            cardIndex = 1;
            break;
        case 'selfie_with_cnic':
            cardIndex = 2;
            break;
        default:
            return;
    }

    const card = documentCards[cardIndex];
    if (!card) return;

    // Update status badge
    const badge = card.querySelector('[data-v0-t="badge"]');
    if (badge) {
        badge.textContent = status;
        badge.className = badge.className.replace(/bg-\w+-600/, getBadgeColor(status));
    }

    // Update status text
    const statusText = card.querySelector('p span.font-medium');
    if (statusText) {
        statusText.textContent = status;
    }

    // Add uploaded date
    const statusContainer = card.querySelector('.space-y-1');
    if (statusContainer && data.uploaded_at) {
        const uploadedDate = statusContainer.querySelector('p:nth-child(2)');
        if (uploadedDate) {
            uploadedDate.innerHTML = `Uploaded: ${formatDate(data.uploaded_at)}`;
        }
    }
}

/**
 * Get badge color class based on status
 */
function getBadgeColor(status) {
    switch(status) {
        case 'approved':
            return 'bg-green-600';
        case 'pending':
            return 'bg-yellow-600';
        case 'rejected':
            return 'bg-red-600';
        default:
            return 'bg-gray-600';
    }
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB');
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        'bg-blue-600'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 4 seconds
    setTimeout(() => {
        notification.remove();
    }, 4000);
}

/**
 * Get KYC status overview
 */
function getKYCStatus() {
    fetch('/api/kyc/status', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAllDocumentStatuses(data.data);
        }
    })
    .catch(error => {
        console.error('Error fetching KYC status:', error);
    });
}

/**
 * Update all document statuses from server data
 */
function updateAllDocumentStatuses(documents) {
    documents.forEach(doc => {
        updateDocumentStatus(doc.type, doc.status, doc);
    });
}

// Load current KYC status on page load
setTimeout(() => {
    getKYCStatus();
}, 1000);
