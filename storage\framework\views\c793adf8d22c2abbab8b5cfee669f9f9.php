

<?php $__env->startSection('title', 'Challenges'); ?>

<?php $__env->startSection('content'); ?>
    <main class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Trading Challenges</h1>
                    <p class="text-muted-foreground">Manage your trading challenges and track progress</p>
                </div><a href="https://your-challenges-store.com" target="_blank" rel="noopener noreferrer"
                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border h-10 px-4 py-2 border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent"><svg
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plus mr-2 h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                    </svg>New Challenge</a>
            </div>
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <div class="rounded-lg border bg-card text-card-foreground shadow-2xs" data-v0-t="card">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold tracking-tight text-lg">Evaluation Challenge #1</h3>
                            <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary text-primary-foreground hover:bg-primary/80"
                                data-v0-t="badge">active</div>
                        </div>
                        <p class="text-sm text-muted-foreground">Phase 1 • Day 8/30</p>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="flex justify-between items-center"><span class="text-sm text-muted-foreground">Current
                                Balance</span><span class="font-semibold">$10750.00</span></div>
                        <div class="flex justify-between items-center"><span
                                class="text-sm text-muted-foreground">P&amp;L</span>
                            <div class="flex items-center space-x-1"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-trending-up h-4 w-4 text-green-500">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                    <polyline points="16 7 22 7 22 13"></polyline>
                                </svg><span class="font-semibold text-green-500">$750.00</span></div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1"><span>Profit Target</span><span>75.0%</span>
                            </div>
                            <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate"
                                data-max="100" class="relative h-4 w-full overflow-hidden rounded-full bg-secondary">
                                <div data-state="indeterminate" data-max="100"
                                    class="h-full w-full flex-1 bg-primary transition-all"
                                    style="transform: translateX(-25%);"></div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <p class="text-muted-foreground">Max Drawdown</p>
                                <p class="font-medium">10%</p>
                            </div>
                            <div>
                                <p class="text-muted-foreground">Daily Drawdown</p>
                                <p class="font-medium">5%</p>
                            </div>
                        </div><button
                            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border-input hover:bg-accent hover:text-accent-foreground border h-10 px-4 py-2 w-full bg-transparent">View
                            Details</button>
                    </div>
                </div>
                <div class="rounded-lg border bg-card text-card-foreground shadow-2xs" data-v0-t="card">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold tracking-tight text-lg">Demo Challenge #2</h3>
                            <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"
                                data-v0-t="badge">completed</div>
                        </div>
                        <p class="text-sm text-muted-foreground">Phase 2 • Day 15/30</p>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="flex justify-between items-center"><span class="text-sm text-muted-foreground">Current
                                Balance</span><span class="font-semibold">$5250.00</span></div>
                        <div class="flex justify-between items-center"><span
                                class="text-sm text-muted-foreground">P&amp;L</span>
                            <div class="flex items-center space-x-1"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-trending-up h-4 w-4 text-green-500">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                    <polyline points="16 7 22 7 22 13"></polyline>
                                </svg><span class="font-semibold text-green-500">$250.00</span></div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1"><span>Profit Target</span><span>100.0%</span>
                            </div>
                            <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate"
                                data-max="100" class="relative h-4 w-full overflow-hidden rounded-full bg-secondary">
                                <div data-state="indeterminate" data-max="100"
                                    class="h-full w-full flex-1 bg-primary transition-all"
                                    style="transform: translateX(0%);"></div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <p class="text-muted-foreground">Max Drawdown</p>
                                <p class="font-medium">10%</p>
                            </div>
                            <div>
                                <p class="text-muted-foreground">Daily Drawdown</p>
                                <p class="font-medium">5%</p>
                            </div>
                        </div><button
                            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border-input hover:bg-accent hover:text-accent-foreground border h-10 px-4 py-2 w-full bg-transparent">View
                            Details</button>
                    </div>
                </div>
            </div>
        </div>
    </main>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('components.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\project\resources\views/challenges.blade.php ENDPATH**/ ?>