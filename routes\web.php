<?php

use App\Http\Controllers\ChallengeController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PayoutRequestsController;
use App\Http\Controllers\KYCController;
use App\Http\Controllers\SupportController;

Route::get('/', [DashboardController::class, 'index'])->name('welcome');
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/challenges', [ChallengeController::class, 'index'])->name('challenges');
Route::get('/payouts', [PayoutRequestsController::class, 'index'])->name('payouts');
Route::get('/kyc', [KycController::class, 'index'])->name('kyc');
Route::get('/support', [SupportController::class, 'index'])->name('support');
Route::get('/profile', [ProfileController::class, 'index'])->name('profile');

// API Routes for AJAX requests
Route::prefix('api')->middleware(['web'])->group(function () {
    // Payout routes
    Route::post('/payouts', [PayoutRequestsController::class, 'store'])->name('api.payouts.store');
    Route::get('/payouts', [PayoutRequestsController::class, 'list'])->name('api.payouts.list');

    // KYC routes
    Route::get('/kyc/status', [KYCController::class, 'status'])->name('api.kyc.status');
    Route::post('/kyc/upload', [KYCController::class, 'upload'])->name('api.kyc.upload');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
