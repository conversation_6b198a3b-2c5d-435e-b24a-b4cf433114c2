<html lang="en" class="dark">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Pak Funding</title>
    {{-- <link class="tempLink" rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family="> --}}
    {{-- <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet"> --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Custom styles for dropdown animations -->
    <style>
        .animate-in {
            animation-duration: 0.2s;
            animation-fill-mode: both;
        }

        .animate-out {
            animation-duration: 0.15s;
            animation-fill-mode: both;
        }

        .fade-in-0 {
            animation-name: fadeIn;
        }

        .fade-out-0 {
            animation-name: fadeOut;
        }

        .zoom-in-95 {
            animation-name: zoomIn95;
        }

        .zoom-out-95 {
            animation-name: zoomOut95;
        }

        .slide-in-from-bottom-2 {
            animation-name: slideInFromBottom2;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        @keyframes zoomIn95 {
            from { transform: scale(0.95); }
            to { transform: scale(1); }
        }

        @keyframes zoomOut95 {
            from { transform: scale(1); }
            to { transform: scale(0.95); }
        }

        @keyframes slideInFromBottom2 {
            from { transform: translateY(8px); }
            to { transform: translateY(0); }
        }
    </style>
</head>

<body class="f_SW50ZXI">
    <div class="flex h-screen bg-background">

        @include('partials.nav')

        <div class="flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in-out">
            
            @include('partials.header')

            @yield('content')
        </div>

    </div>

    <!-- Navigation JavaScript -->
    <script src="{{ asset('js/navigation.js') }}"></script>

    @stack('scripts')
    @yield('scripts')
</body>

</html>
