// Navigation JavaScript functionality
document.addEventListener('DOMContentLoaded', function () {
    console.log('Navigation loaded');

    // Initialize dropdown functionality
    initializeUserDropdown();
});

/**
 * Initialize user dropdown menu functionality
 */
function initializeUserDropdown() {
    const menuButton = document.getElementById('user-menu-button');
    const dropdownMenu = document.getElementById('user-dropdown-menu');
    const chevronIcon = document.getElementById('chevron-icon');
    const logoutButton = document.getElementById('logout-button');

    if (!menuButton || !dropdownMenu) {
        console.warn('User dropdown elements not found');
        return;
    }

    // Toggle dropdown on button click
    menuButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleDropdown();
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!menuButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
            closeDropdown();
        }
    });

    // Close dropdown on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDropdown();
        }
    });

    // Handle logout button click
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
        });
    }

    /**
     * Toggle dropdown visibility
     */
    function toggleDropdown() {
        const isOpen = !dropdownMenu.classList.contains('hidden');
        
        if (isOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }

    /**
     * Open dropdown menu
     */
    function openDropdown() {
        dropdownMenu.classList.remove('hidden');
        menuButton.setAttribute('aria-expanded', 'true');
        menuButton.setAttribute('data-state', 'open');
        
        // Rotate chevron icon
        if (chevronIcon) {
            chevronIcon.style.transform = 'rotate(180deg)';
        }

        // Add animation classes
        dropdownMenu.classList.add('animate-in', 'fade-in-0', 'zoom-in-95', 'slide-in-from-bottom-2');
        
        // Remove animation classes after animation completes
        setTimeout(() => {
            dropdownMenu.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95', 'slide-in-from-bottom-2');
        }, 200);
    }

    /**
     * Close dropdown menu
     */
    function closeDropdown() {
        // Add closing animation classes
        dropdownMenu.classList.add('animate-out', 'fade-out-0', 'zoom-out-95');
        
        menuButton.setAttribute('aria-expanded', 'false');
        menuButton.setAttribute('data-state', 'closed');
        
        // Reset chevron icon
        if (chevronIcon) {
            chevronIcon.style.transform = 'rotate(0deg)';
        }

        // Hide after animation
        setTimeout(() => {
            dropdownMenu.classList.add('hidden');
            dropdownMenu.classList.remove('animate-out', 'fade-out-0', 'zoom-out-95');
        }, 150);
    }

    /**
     * Handle logout functionality
     */
    function handleLogout() {
        // Show confirmation dialog
        if (confirm('Are you sure you want to log out?')) {
            // Close dropdown first
            closeDropdown();
            
            // Show loading state
            showNotification('Logging out...', 'info');
            
            // Simulate logout process (replace with actual logout logic)
            setTimeout(() => {
                // Redirect to login page or perform actual logout
                window.location.href = '/login';
                
                // Or if you have a logout route:
                // window.location.href = '/logout';
                
                // Or if you want to make an AJAX logout request:
                // performLogout();
            }, 1000);
        }
    }

    /**
     * Perform AJAX logout (alternative to redirect)
     */
    function performLogout() {
        fetch('/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => {
            if (response.ok) {
                showNotification('Logged out successfully', 'success');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1000);
            } else {
                throw new Error('Logout failed');
            }
        })
        .catch(error => {
            console.error('Logout error:', error);
            showNotification('Error logging out. Please try again.', 'error');
        });
    }
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        'bg-blue-600'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

/**
 * Initialize mobile menu toggle (if needed)
 */
function initializeMobileMenu() {
    // Add mobile menu functionality here if needed
    console.log('Mobile menu functionality can be added here');
}
