// Payouts page JavaScript functionality
document.addEventListener('DOMContentLoaded', function () {
    console.log('Payouts page loaded');

    // Initialize payout functionality
    initializePayoutRequests();
    initializePayoutHistory();
});

/**
 * Initialize payout request functionality
 */
function initializePayoutRequests() {
    // Get the payout request button
    const payoutButton = document.querySelector('[aria-haspopup="dialog"]');

    if (payoutButton) {
        payoutButton.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Payout request button clicked');
            openPayoutModal();
        });
    }
}

/**
 * Initialize payout history functionality
 */
function initializePayoutHistory() {
    // Add any interactive functionality for payout history items
    const payoutItems = document.querySelectorAll('.border.border-border.rounded-lg.p-4');

    payoutItems.forEach(item => {
        item.addEventListener('click', function () {
            console.log('Payout item clicked');
            // Add functionality to view payout details
        });
    });
}

/**
 * Open payout request modal
 */
function openPayoutModal() {
    // Create modal backdrop and container
    const modalHTML = `
        <div id="payoutModalBackdrop" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4" style="z-index: 10000;">

            <div role="dialog" id="payoutModal" aria-describedby="modal-description" aria-labelledby="modal-title"
                class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-6 max-h-screen overflow-y-auto" style="z-index: 10001;">

                <!-- Close button -->
                <button type="button" id="closeModalBtn"
                    class="absolute right-4 top-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 bg-transparent border-0 cursor-pointer p-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>

                <!-- Modal header -->
                <div class="mb-6">
                    <h2 id="modal-title" class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Request New Payout</h2>
                    <p id="modal-description" class="text-sm text-gray-600 dark:text-gray-400">Submit a request to withdraw your profits</p>
                </div>

                <!-- Modal form -->
                <form id="payoutForm" class="space-y-4"action="">
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Amount ($)
                        </label>
                        <input type="number" id="amount" name="amount" min="1" step="0.01" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            placeholder="e.g., 500">
                    </div>

                    <div>
                        <label for="method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Payout Method
                        </label>
                        <select id="method" name="method" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">Select method</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="paypal">PayPal</option>
                            <option value="crypto">Cryptocurrency</option>
                        </select>
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea id="notes" name="notes" rows="3"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-y focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            placeholder="Any specific instructions or details..."></textarea>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelPayoutBtn"
                            class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer text-sm">
                            Cancel
                        </button>
                        <button type="submit" id="submitPayoutBtn"
                            class="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 cursor-pointer text-sm border-0">
                            Submit Payout Request
                        </button>
                    </div>
                </form>
            </div>
            
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listeners for modal
    setupModalEventListeners();
}

/**
 * Setup event listeners for the payout modal
 */
function setupModalEventListeners() {
    const backdrop = document.getElementById('payoutModalBackdrop');
    const closeBtn = document.getElementById('closeModalBtn');
    const cancelBtn = document.getElementById('cancelPayoutBtn');
    const form = document.getElementById('payoutForm');

    // Close modal handlers
    if (closeBtn) {
        closeBtn.addEventListener('click', closePayoutModal);
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', closePayoutModal);
    }

    // Close on backdrop click (but not when clicking the modal itself)
    if (backdrop) {
        backdrop.addEventListener('click', function(e) {
            if (e.target === backdrop) {
                closePayoutModal();
            }
        });
    }

    // Close on Escape key
    document.addEventListener('keydown', handleEscapeKey);

    // Handle form submission
    if (form) {
        form.addEventListener('submit', handlePayoutSubmission);
    }
}

/**
 * Close the payout modal
 */
function closePayoutModal() {
    const modal = document.getElementById('payoutModal');
    const backdrop = document.getElementById('payoutModalBackdrop');

    // Remove modal elements
    if (modal) {
        modal.remove();
    }
    if (backdrop) {
        backdrop.remove();
    }

    // Remove the escape key listener
    document.removeEventListener('keydown', handleEscapeKey);
}

/**
 * Handle escape key press
 */
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        closePayoutModal();
    }
}

/**
 * Handle payout form submission
 */
function handlePayoutSubmission(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const payoutData = {
        amount: formData.get('amount'),
        method: formData.get('method'),
        notes: formData.get('notes')
    };

    console.log('Payout request data:', payoutData);

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Processing...';
    submitButton.disabled = true;

    // Send the data to Laravel backend
    fetch('/api/payouts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(payoutData)
    })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            console.log('Success:', data);
            showNotification(data.message || 'Payout request submitted successfully!', 'success');
            closePayoutModal();

            // Optionally refresh the page to show updated payout history
            // location.reload();
        })
        .catch(error => {
            console.error('Error:', error);

            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;

            // Show error message
            let errorMessage = 'Error submitting payout request. Please try again.';
            if (error.errors) {
                // Handle validation errors
                const firstError = Object.values(error.errors)[0];
                if (firstError && firstError.length > 0) {
                    errorMessage = firstError[0];
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            showNotification(errorMessage, 'error');
        });
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
            'bg-blue-600'
        }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
