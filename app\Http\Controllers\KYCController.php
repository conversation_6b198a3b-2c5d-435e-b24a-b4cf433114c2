<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class KYCController extends Controller
{
    public function index()
    {
        return view('kyc');
    }

    /**
     * Get KYC status for the user
     *
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        try {
            // TODO: Replace with actual database query
            // For now, return mock data that matches the frontend
            $documents = [
                [
                    'id' => 1,
                    'type' => 'cnic_front',
                    'status' => 'approved',
                    'uploaded_at' => '2024-01-15',
                    'reviewed_at' => '2024-01-16',
                    'filename' => 'cnic_front_123.jpg'
                ],
                [
                    'id' => 2,
                    'type' => 'cnic_back',
                    'status' => 'pending',
                    'uploaded_at' => '2024-01-15',
                    'reviewed_at' => null,
                    'filename' => 'cnic_back_123.jpg'
                ],
                [
                    'id' => 3,
                    'type' => 'selfie_with_cnic',
                    'status' => 'not_uploaded',
                    'uploaded_at' => null,
                    'reviewed_at' => null,
                    'filename' => null
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $documents
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching KYC status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching KYC status.'
            ], 500);
        }
    }

    /**
     * Upload KYC document
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            // Validate the request data
            $validator = Validator::make($request->all(), [
                'document' => 'required|file|mimes:jpeg,jpg,png,pdf|max:5120', // 5MB max
                'type' => 'required|string|in:cnic_front,cnic_back,selfie_with_cnic'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validatedData = $validator->validated();
            $file = $request->file('document');
            $documentType = $validatedData['type'];

            // Generate unique filename
            $filename = $documentType . '_' . time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file (you might want to use a different disk for production)
            $path = $file->storeAs('kyc_documents', $filename, 'public');

            // Log the upload
            Log::info('KYC document uploaded', [
                'type' => $documentType,
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                // 'user_id' => auth()->id() ?? 'guest', // If you have authentication
                'timestamp' => now()
            ]);

            // TODO: Save to database
            // $kycDocument = KYCDocument::updateOrCreate([
            //     'user_id' => auth()->id(),
            //     'type' => $documentType
            // ], [
            //     'filename' => $filename,
            //     'original_name' => $file->getClientOriginalName(),
            //     'file_path' => $path,
            //     'status' => 'pending',
            //     'uploaded_at' => now()
            // ]);

            return response()->json([
                'success' => true,
                'message' => 'Document uploaded successfully',
                'data' => [
                    'type' => $documentType,
                    'filename' => $filename,
                    'status' => 'pending',
                    'uploaded_at' => now()->toISOString()
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error uploading KYC document', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading the document. Please try again.'
            ], 500);
        }
    }
}