<html lang="en" class="dark">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="/_next/static/css/5e5c87ba0fcdd97e.css?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH"
        data-precedence="next">
    <link rel="stylesheet" href="/_next/static/css/8c27ffaabc6186da.css?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH"
        data-precedence="next">
    <link rel="stylesheet" href="/_next/static/css/c5fa0d52ed50ebcc.css?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH"
        data-precedence="next">
    <link rel="preload" as="script" fetchpriority="low"
        href="/_next/static/chunks/webpack-8d8ca88a8662b623.js?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH">
    <script src="/_next/static/chunks/6d551bf0-196e26339e93aea2.js?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH" async=""></script>
    <script src="/_next/static/chunks/5370-6a7812e41f69e405.js?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH" async=""></script>
    <script src="/_next/static/chunks/main-app-04222f2c3df7cca4.js?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH" async=""></script>
    <meta name="next-size-adjust" content="">
    <title>v0</title>
    <script src="/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH" nomodule="">
    </script>
    <link rel="preload" href="/_next/static/media/66f30814ff6d7cdf.p.woff2?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH"
        as="font" crossorigin="" type="font/woff2">
    <link rel="preload" href="/_next/static/media/e11418ac562b8ac1-s.p.woff2?dpl=dpl_9Po7zmEN84aN5iYjhVK6iwtYpegH"
        as="font" crossorigin="" type="font/woff2">
    <script src="/assets/tailwind-v3.js?v=1" async="" fetchpriority="high"></script>
    <style>
        @tailwind base;
        @tailwind components;
        @tailwind utilities;

        *,
        ::before,
        ::after {
            --tw-border-spacing-x: 0;
            --tw-border-spacing-y: 0;
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-rotate: 0;
            --tw-skew-x: 0;
            --tw-skew-y: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-pan-x: ;
            --tw-pan-y: ;
            --tw-pinch-zoom: ;
            --tw-scroll-snap-strictness: proximity;
            --tw-gradient-from-position: ;
            --tw-gradient-via-position: ;
            --tw-gradient-to-position: ;
            --tw-ordinal: ;
            --tw-slashed-zero: ;
            --tw-numeric-figure: ;
            --tw-numeric-spacing: ;
            --tw-numeric-fraction: ;
            --tw-ring-inset: ;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgb(59 130 246 / 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            --tw-blur: ;
            --tw-brightness: ;
            --tw-contrast: ;
            --tw-grayscale: ;
            --tw-hue-rotate: ;
            --tw-invert: ;
            --tw-saturate: ;
            --tw-sepia: ;
            --tw-drop-shadow: ;
            --tw-backdrop-blur: ;
            --tw-backdrop-brightness: ;
            --tw-backdrop-contrast: ;
            --tw-backdrop-grayscale: ;
            --tw-backdrop-hue-rotate: ;
            --tw-backdrop-invert: ;
            --tw-backdrop-opacity: ;
            --tw-backdrop-saturate: ;
            --tw-backdrop-sepia: ;
            --tw-contain-size: ;
            --tw-contain-layout: ;
            --tw-contain-paint: ;
            --tw-contain-style:
        }

        ::backdrop {
            --tw-border-spacing-x: 0;
            --tw-border-spacing-y: 0;
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-rotate: 0;
            --tw-skew-x: 0;
            --tw-skew-y: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-pan-x: ;
            --tw-pan-y: ;
            --tw-pinch-zoom: ;
            --tw-scroll-snap-strictness: proximity;
            --tw-gradient-from-position: ;
            --tw-gradient-via-position: ;
            --tw-gradient-to-position: ;
            --tw-ordinal: ;
            --tw-slashed-zero: ;
            --tw-numeric-figure: ;
            --tw-numeric-spacing: ;
            --tw-numeric-fraction: ;
            --tw-ring-inset: ;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgb(59 130 246 / 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            --tw-blur: ;
            --tw-brightness: ;
            --tw-contrast: ;
            --tw-grayscale: ;
            --tw-hue-rotate: ;
            --tw-invert: ;
            --tw-saturate: ;
            --tw-sepia: ;
            --tw-drop-shadow: ;
            --tw-backdrop-blur: ;
            --tw-backdrop-brightness: ;
            --tw-backdrop-contrast: ;
            --tw-backdrop-grayscale: ;
            --tw-backdrop-hue-rotate: ;
            --tw-backdrop-invert: ;
            --tw-backdrop-opacity: ;
            --tw-backdrop-saturate: ;
            --tw-backdrop-sepia: ;
            --tw-contain-size: ;
            --tw-contain-layout: ;
            --tw-contain-paint: ;
            --tw-contain-style:
        }

        /* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */
        *,
        ::after,
        ::before {
            box-sizing: border-box;
            border-width: 0;
            border-style: solid;
            border-color: #e5e7eb
        }

        ::after,
        ::before {
            --tw-content: ''
        }

        :host,
        html {
            line-height: 1.5;
            -webkit-text-size-adjust: 100%;
            -moz-tab-size: 4;
            tab-size: 4;
            font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-feature-settings: normal;
            font-variation-settings: normal;
            -webkit-tap-highlight-color: transparent
        }

        body {
            margin: 0;
            line-height: inherit
        }

        hr {
            height: 0;
            color: inherit;
            border-top-width: 1px
        }

        abbr:where([title]) {
            -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-size: inherit;
            font-weight: inherit
        }

        a {
            color: inherit;
            text-decoration: inherit
        }

        b,
        strong {
            font-weight: bolder
        }

        code,
        kbd,
        pre,
        samp {
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-feature-settings: normal;
            font-variation-settings: normal;
            font-size: 1em
        }

        small {
            font-size: 80%
        }

        sub,
        sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline
        }

        sub {
            bottom: -.25em
        }

        sup {
            top: -.5em
        }

        table {
            text-indent: 0;
            border-color: inherit;
            border-collapse: collapse
        }

        button,
        input,
        optgroup,
        select,
        textarea {
            font-family: inherit;
            font-feature-settings: inherit;
            font-variation-settings: inherit;
            font-size: 100%;
            font-weight: inherit;
            line-height: inherit;
            letter-spacing: inherit;
            color: inherit;
            margin: 0;
            padding: 0
        }

        button,
        select {
            text-transform: none
        }

        button,
        input:where([type=button]),
        input:where([type=reset]),
        input:where([type=submit]) {
            -webkit-appearance: button;
            background-color: transparent;
            background-image: none
        }

        :-moz-focusring {
            outline: auto
        }

        :-moz-ui-invalid {
            box-shadow: none
        }

        progress {
            vertical-align: baseline
        }

        ::-webkit-inner-spin-button,
        ::-webkit-outer-spin-button {
            height: auto
        }

        [type=search] {
            -webkit-appearance: textfield;
            outline-offset: -2px
        }

        ::-webkit-search-decoration {
            -webkit-appearance: none
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit
        }

        summary {
            display: list-item
        }

        blockquote,
        dd,
        dl,
        figure,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        hr,
        p,
        pre {
            margin: 0
        }

        fieldset {
            margin: 0;
            padding: 0
        }

        legend {
            padding: 0
        }

        menu,
        ol,
        ul {
            list-style: none;
            margin: 0;
            padding: 0
        }

        dialog {
            padding: 0
        }

        textarea {
            resize: vertical
        }

        input::placeholder,
        textarea::placeholder {
            opacity: 1;
            color: #9ca3af
        }

        [role=button],
        button {
            cursor: pointer
        }

        :disabled {
            cursor: default
        }

        audio,
        canvas,
        embed,
        iframe,
        img,
        object,
        svg,
        video {
            display: block;
            vertical-align: middle
        }

        img,
        video {
            max-width: 100%;
            height: auto
        }

        [hidden]:where(:not([hidden=until-found])) {
            display: none
        }

        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 221.2 83.2% 53.3%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 221.2 83.2% 53.3%;
            --radius: .5rem
        }

        .dark {
            --background: 220 13% 13%;
            --foreground: 210 40% 98%;
            --card: 220 13% 18%;
            --card-foreground: 210 40% 98%;
            --popover: 220 13% 18%;
            --popover-foreground: 210 40% 98%;
            --primary: 140 60% 40%;
            --primary-foreground: 210 40% 98%;
            --secondary: 220 13% 25%;
            --secondary-foreground: 210 40% 98%;
            --muted: 220 13% 20%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 220 13% 25%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 220 13% 25%;
            --input: 220 13% 20%;
            --ring: 140 60% 40%
        }

        * {
            border-color: hsl(var(--border))
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground))
        }

        .fixed {
            position: fixed
        }

        .absolute {
            position: absolute
        }

        .relative {
            position: relative
        }

        .top-0 {
            top: 0px
        }

        .bottom-4 {
            bottom: 1rem
        }

        .left-4 {
            left: 1rem
        }

        .z-50 {
            z-index: 50
        }

        .-mx-1 {
            margin-left: -0.25rem;
            margin-right: -0.25rem
        }

        .my-1 {
            margin-top: 0.25rem;
            margin-bottom: 0.25rem
        }

        .mb-4 {
            margin-bottom: 1rem
        }

        .mr-3 {
            margin-right: 0.75rem
        }

        .mb-1 {
            margin-bottom: 0.25rem
        }

        .mb-2 {
            margin-bottom: 0.5rem
        }

        .mr-2 {
            margin-right: 0.5rem
        }

        .mt-4 {
            margin-top: 1rem
        }

        .mt-6 {
            margin-top: 1.5rem
        }

        .flex {
            display: flex
        }

        .inline-flex {
            display: inline-flex
        }

        .grid {
            display: grid
        }

        .hidden {
            display: none
        }

        .h-32 {
            height: 8rem
        }

        .h-10 {
            height: 2.5rem
        }

        .h-16 {
            height: 4rem
        }

        .h-2 {
            height: 0.5rem
        }

        .h-4 {
            height: 1rem
        }

        .h-5 {
            height: 1.25rem
        }

        .h-6 {
            height: 1.5rem
        }

        .h-64 {
            height: 16rem
        }

        .h-8 {
            height: 2rem
        }

        .h-auto {
            height: auto
        }

        .h-full {
            height: 100%
        }

        .h-screen {
            height: 100vh
        }

        .h-3 {
            height: 0.75rem
        }

        .h-9 {
            height: 2.25rem
        }

        .h-\[200px\] {
            height: 200px
        }

        .h-px {
            height: 1px
        }

        .h-12 {
            height: 3rem
        }

        .max-h-screen {
            max-height: 100vh
        }

        .min-h-screen {
            min-height: 100vh
        }

        .min-h-\[80px\] {
            min-height: 80px
        }

        .w-32 {
            width: 8rem
        }

        .w-full {
            width: 100%
        }

        .w-10 {
            width: 2.5rem
        }

        .w-2 {
            width: 0.5rem
        }

        .w-4 {
            width: 1rem
        }

        .w-5 {
            width: 1.25rem
        }

        .w-6 {
            width: 1.5rem
        }

        .w-8 {
            width: 2rem
        }

        .w-3 {
            width: 0.75rem
        }

        .w-56 {
            width: 14rem
        }

        .w-12 {
            width: 3rem
        }

        .min-w-32 {
            min-width: 8rem
        }

        .max-w-md {
            max-width: 28rem
        }

        .flex-1 {
            flex: 1 1 0%
        }

        .shrink-0 {
            flex-shrink: 0
        }

        @keyframes spin {
            to {
                transform: rotate(360deg)
            }
        }

        .animate-spin {
            animation: spin 1s linear infinite
        }

        .cursor-default {
            cursor: default
        }

        .select-none {
            -webkit-user-select: none;
            user-select: none
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr))
        }

        .flex-row {
            flex-direction: row
        }

        .flex-col {
            flex-direction: column
        }

        .flex-col-reverse {
            flex-direction: column-reverse
        }

        .items-center {
            align-items: center
        }

        .justify-start {
            justify-content: flex-start
        }

        .justify-end {
            justify-content: flex-end
        }

        .justify-center {
            justify-content: center
        }

        .justify-between {
            justify-content: space-between
        }

        .gap-2 {
            gap: 0.5rem
        }

        .gap-4 {
            gap: 1rem
        }

        .gap-6 {
            gap: 1.5rem
        }

        .space-x-2> :not([hidden])~ :not([hidden]) {
            --tw-space-x-reverse: 0;
            margin-right: calc(0.5rem * var(--tw-space-x-reverse));
            margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))
        }

        .space-x-3> :not([hidden])~ :not([hidden]) {
            --tw-space-x-reverse: 0;
            margin-right: calc(0.75rem * var(--tw-space-x-reverse));
            margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))
        }

        .space-x-4> :not([hidden])~ :not([hidden]) {
            --tw-space-x-reverse: 0;
            margin-right: calc(1rem * var(--tw-space-x-reverse));
            margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
        }

        .space-y-0\.5> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.125rem * var(--tw-space-y-reverse))
        }

        .space-y-1\.5> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.375rem * var(--tw-space-y-reverse))
        }

        .space-y-2> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.5rem * var(--tw-space-y-reverse))
        }

        .space-y-3> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.75rem * var(--tw-space-y-reverse))
        }

        .space-y-4> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(1rem * var(--tw-space-y-reverse))
        }

        .space-y-6> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
        }

        .space-y-1> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.25rem * var(--tw-space-y-reverse))
        }

        .overflow-hidden {
            overflow: hidden
        }

        .overflow-x-auto {
            overflow-x: auto
        }

        .overflow-y-auto {
            overflow-y: auto
        }

        .whitespace-nowrap {
            white-space: nowrap
        }

        .rounded-full {
            border-radius: 9999px
        }

        .rounded-md {
            border-radius: calc(var(--radius) - 2px)
        }

        .rounded {
            border-radius: 0.25rem
        }

        .rounded-lg {
            border-radius: var(--radius)
        }

        .rounded-sm {
            border-radius: calc(var(--radius) - 4px)
        }

        .border {
            border-width: 1px
        }

        .border-b-2 {
            border-bottom-width: 2px
        }

        .border-b {
            border-bottom-width: 1px
        }

        .border-t {
            border-top-width: 1px
        }

        .border-primary {
            border-color: hsl(var(--primary))
        }

        .border-slate-600 {
            --tw-border-opacity: 1;
            border-color: rgb(71 85 105 / var(--tw-border-opacity, 1))
        }

        .border-slate-700 {
            --tw-border-opacity: 1;
            border-color: rgb(51 65 85 / var(--tw-border-opacity, 1))
        }

        .border-border {
            border-color: hsl(var(--border))
        }

        .border-green-500 {
            --tw-border-opacity: 1;
            border-color: rgb(34 197 94 / var(--tw-border-opacity, 1))
        }

        .border-input {
            border-color: hsl(var(--input))
        }

        .border-transparent {
            border-color: transparent
        }

        .border-yellow-600 {
            --tw-border-opacity: 1;
            border-color: rgb(202 138 4 / var(--tw-border-opacity, 1))
        }

        .border-gray-200 {
            --tw-border-opacity: 1;
            border-color: rgb(229 231 235 / var(--tw-border-opacity, 1))
        }

        .border-gray-300 {
            --tw-border-opacity: 1;
            border-color: rgb(209 213 219 / var(--tw-border-opacity, 1))
        }

        .bg-background {
            background-color: hsl(var(--background))
        }

        .bg-green-500 {
            --tw-bg-opacity: 1;
            background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1))
        }

        .bg-slate-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1))
        }

        .bg-slate-700 {
            --tw-bg-opacity: 1;
            background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1))
        }

        .bg-slate-800 {
            --tw-bg-opacity: 1;
            background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1))
        }

        .bg-card {
            background-color: hsl(var(--card))
        }

        .bg-gray-800 {
            --tw-bg-opacity: 1;
            background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1))
        }

        .bg-primary {
            background-color: hsl(var(--primary))
        }

        .bg-red-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1))
        }

        .bg-secondary {
            background-color: hsl(var(--secondary))
        }

        .bg-transparent {
            background-color: transparent
        }

        .bg-yellow-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1))
        }

        .bg-yellow-900\/20 {
            background-color: rgb(113 63 18 / 0.2)
        }

        .bg-input {
            background-color: hsl(var(--input))
        }

        .bg-muted {
            background-color: hsl(var(--muted))
        }

        .bg-popover {
            background-color: hsl(var(--popover))
        }

        .bg-\[\#1a4d3a\] {
            --tw-bg-opacity: 1;
            background-color: rgb(26 77 58 / var(--tw-bg-opacity, 1))
        }

        .bg-gray-50 {
            --tw-bg-opacity: 1;
            background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1))
        }

        .bg-white {
            --tw-bg-opacity: 1;
            background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1))
        }

        .p-4 {
            padding: 1rem
        }

        .p-6 {
            padding: 1.5rem
        }

        .p-3 {
            padding: 0.75rem
        }

        .p-1 {
            padding: 0.25rem
        }

        .px-2 {
            padding-left: 0.5rem;
            padding-right: 0.5rem
        }

        .px-3 {
            padding-left: 0.75rem;
            padding-right: 0.75rem
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem
        }

        .px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem
        }

        .py-1\.5 {
            padding-top: 0.375rem;
            padding-bottom: 0.375rem
        }

        .py-2 {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem
        }

        .py-3 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem
        }

        .py-6 {
            padding-top: 1.5rem;
            padding-bottom: 1.5rem
        }

        .px-2\.5 {
            padding-left: 0.625rem;
            padding-right: 0.625rem
        }

        .py-0\.5 {
            padding-top: 0.125rem;
            padding-bottom: 0.125rem
        }

        .py-2\.5 {
            padding-top: 0.625rem;
            padding-bottom: 0.625rem
        }

        .py-12 {
            padding-top: 3rem;
            padding-bottom: 3rem
        }

        .pt-0 {
            padding-top: 0px
        }

        .pb-6 {
            padding-bottom: 1.5rem
        }

        .text-left {
            text-align: left
        }

        .text-center {
            text-align: center
        }

        .text-right {
            text-align: right
        }

        .text-lg {
            font-size: 1.125rem;
            line-height: 1.75rem
        }

        .text-sm {
            font-size: 0.875rem;
            line-height: 1.25rem
        }

        .text-xs {
            font-size: 0.75rem;
            line-height: 1rem
        }

        .text-2xl {
            font-size: 1.5rem;
            line-height: 2rem
        }

        .text-3xl {
            font-size: 1.875rem;
            line-height: 2.25rem
        }

        .text-xl {
            font-size: 1.25rem;
            line-height: 1.75rem
        }

        .font-bold {
            font-weight: 700
        }

        .font-medium {
            font-weight: 500
        }

        .font-semibold {
            font-weight: 600
        }

        .font-normal {
            font-weight: 400
        }

        .uppercase {
            text-transform: uppercase
        }

        .leading-none {
            line-height: 1
        }

        .tracking-wider {
            letter-spacing: 0.05em
        }

        .tracking-tight {
            letter-spacing: -0.025em
        }

        .text-gray-400 {
            --tw-text-opacity: 1;
            color: rgb(156 163 175 / var(--tw-text-opacity, 1))
        }

        .text-white {
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1))
        }

        .text-card-foreground {
            color: hsl(var(--card-foreground))
        }

        .text-green-400 {
            --tw-text-opacity: 1;
            color: rgb(74 222 128 / var(--tw-text-opacity, 1))
        }

        .text-muted-foreground {
            color: hsl(var(--muted-foreground))
        }

        .text-red-500 {
            --tw-text-opacity: 1;
            color: rgb(239 68 68 / var(--tw-text-opacity, 1))
        }

        .text-yellow-500 {
            --tw-text-opacity: 1;
            color: rgb(234 179 8 / var(--tw-text-opacity, 1))
        }

        .text-foreground {
            color: hsl(var(--foreground))
        }

        .text-popover-foreground {
            color: hsl(var(--popover-foreground))
        }

        .text-gray-600 {
            --tw-text-opacity: 1;
            color: rgb(75 85 99 / var(--tw-text-opacity, 1))
        }

        .text-gray-700 {
            --tw-text-opacity: 1;
            color: rgb(55 65 81 / var(--tw-text-opacity, 1))
        }

        .text-gray-800 {
            --tw-text-opacity: 1;
            color: rgb(31 41 55 / var(--tw-text-opacity, 1))
        }

        .text-gray-900 {
            --tw-text-opacity: 1;
            color: rgb(17 24 39 / var(--tw-text-opacity, 1))
        }

        .text-\[\#1a4d3a\] {
            --tw-text-opacity: 1;
            color: rgb(26 77 58 / var(--tw-text-opacity, 1))
        }

        .text-black {
            --tw-text-opacity: 1;
            color: rgb(0 0 0 / var(--tw-text-opacity, 1))
        }

        .text-gray-500 {
            --tw-text-opacity: 1;
            color: rgb(107 114 128 / var(--tw-text-opacity, 1))
        }

        .placeholder-gray-500::placeholder {
            --tw-placeholder-opacity: 1;
            color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1))
        }

        .shadow-md {
            --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
        }

        .shadow-lg {
            --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
        }

        .shadow-xl {
            --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
        }

        .ring-offset-background {
            --tw-ring-offset-color: hsl(var(--background))
        }

        .transition-all {
            transition-property: all;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms
        }

        .transition-colors {
            transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms
        }

        .duration-300 {
            transition-duration: 300ms
        }

        .ease-in-out {
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
        }

        @keyframes enter {
            from {
                opacity: var(--tw-enter-opacity, 1);
                transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))
            }
        }

        @keyframes exit {
            to {
                opacity: var(--tw-exit-opacity, 1);
                transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))
            }
        }

        .duration-300 {
            animation-duration: 300ms
        }

        .ease-in-out {
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
        }

        .file\:border-0::file-selector-button {
            border-width: 0px
        }

        .file\:bg-transparent::file-selector-button {
            background-color: transparent
        }

        .file\:text-sm::file-selector-button {
            font-size: 0.875rem;
            line-height: 1.25rem
        }

        .file\:font-medium::file-selector-button {
            font-weight: 500
        }

        .file\:text-foreground::file-selector-button {
            color: hsl(var(--foreground))
        }

        .placeholder\:text-muted-foreground::placeholder {
            color: hsl(var(--muted-foreground))
        }

        .placeholder\:text-gray-400::placeholder {
            --tw-text-opacity: 1;
            color: rgb(156 163 175 / var(--tw-text-opacity, 1))
        }

        .last\:border-b-0:last-child {
            border-bottom-width: 0px
        }

        .hover\:bg-slate-700:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1))
        }

        .hover\:bg-accent:hover {
            background-color: hsl(var(--accent))
        }

        .hover\:bg-green-500:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1))
        }

        .hover\:bg-green-600:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1))
        }

        .hover\:bg-primary\/80:hover {
            background-color: hsl(var(--primary) / 0.8)
        }

        .hover\:bg-secondary\/80:hover {
            background-color: hsl(var(--secondary) / 0.8)
        }

        .hover\:bg-\[\#1a4d3a\]\/90:hover {
            background-color: rgb(26 77 58 / 0.9)
        }

        .hover\:text-accent-foreground:hover {
            color: hsl(var(--accent-foreground))
        }

        .hover\:text-white:hover {
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1))
        }

        .hover\:underline:hover {
            -webkit-text-decoration-line: underline;
            text-decoration-line: underline
        }

        .focus\:border-gray-800:focus {
            --tw-border-opacity: 1;
            border-color: rgb(31 41 55 / var(--tw-border-opacity, 1))
        }

        .focus\:border-\[\#1a4d3a\]:focus {
            --tw-border-opacity: 1;
            border-color: rgb(26 77 58 / var(--tw-border-opacity, 1))
        }

        .focus\:bg-accent:focus {
            background-color: hsl(var(--accent))
        }

        .focus\:text-accent-foreground:focus {
            color: hsl(var(--accent-foreground))
        }

        .focus\:ring-2:focus {
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
        }

        .focus\:ring-ring:focus {
            --tw-ring-color: hsl(var(--ring))
        }

        .focus\:ring-gray-800:focus {
            --tw-ring-opacity: 1;
            --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity, 1))
        }

        .focus\:ring-\[\#1a4d3a\]:focus {
            --tw-ring-opacity: 1;
            --tw-ring-color: rgb(26 77 58 / var(--tw-ring-opacity, 1))
        }

        .focus\:ring-offset-2:focus {
            --tw-ring-offset-width: 2px
        }

        .focus-visible\:ring-2:focus-visible {
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
        }

        .focus-visible\:ring-ring:focus-visible {
            --tw-ring-color: hsl(var(--ring))
        }

        .focus-visible\:ring-offset-2:focus-visible {
            --tw-ring-offset-width: 2px
        }

        .disabled\:pointer-events-none:disabled {
            pointer-events: none
        }

        .disabled\:cursor-not-allowed:disabled {
            cursor: not-allowed
        }

        .disabled\:opacity-50:disabled {
            opacity: 0.5
        }

        .peer:disabled~.peer-disabled\:cursor-not-allowed {
            cursor: not-allowed
        }

        .peer:disabled~.peer-disabled\:opacity-70{opacity:0.7}.data-\[state\=open\]\:animate-in[data-state="open"] {
            animation-name: enter;
            animation-duration: 150ms;
            --tw-enter-opacity: initial;
            --tw-enter-scale: initial;
            --tw-enter-rotate: initial;
            --tw-enter-translate-x: initial;
            --tw-enter-translate-y: initial
        }

        .data-\[state\=closed\]\:animate-out[data-state="closed"] {
            animation-name: exit;
            animation-duration: 150ms;
            --tw-exit-opacity: initial;
            --tw-exit-scale: initial;
            --tw-exit-rotate: initial;
            --tw-exit-translate-x: initial;
            --tw-exit-translate-y: initial
        }

        .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
            --tw-exit-opacity: 0
        }

        .data-\[state\=open\]\:fade-in-0[data-state="open"] {
            --tw-enter-opacity: 0
        }

        .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
            --tw-exit-scale: .95
        }

        .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
            --tw-enter-scale: .95
        }

        .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
            --tw-enter-translate-y: -0.5rem
        }

        .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
            --tw-enter-translate-x: 0.5rem
        }

        .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
            --tw-enter-translate-x: -0.5rem
        }

        .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
            --tw-enter-translate-y: 0.5rem
        }

        @media (min-width: 640px) {
            .sm\:bottom-0 {
                bottom: 0px
            }

            .sm\:right-0 {
                right: 0px
            }

            .sm\:top-auto {
                top: auto
            }

            .sm\:flex-col {
                flex-direction: column
            }

            .sm\:px-6 {
                padding-left: 1.5rem;
                padding-right: 1.5rem
            }
        }

        @media (min-width: 768px) {
            .md\:col-span-2 {
                grid-column: span 2 / span 2
            }

            .md\:flex {
                display: flex
            }

            .md\:hidden {
                display: none
            }

            .md\:w-56 {
                width: 14rem
            }

            .md\:max-w-\[420px\] {
                max-width: 420px
            }

            .md\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr))
            }

            .md\:grid-cols-6 {
                grid-template-columns: repeat(6, minmax(0, 1fr))
            }

            .md\:flex-col {
                flex-direction: column
            }
        }

        @media (min-width: 1024px) {
            .lg\:col-span-2 {
                grid-column: span 2 / span 2
            }

            .lg\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr))
            }

            .lg\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr))
            }

            .lg\:grid-cols-5 {
                grid-template-columns: repeat(5, minmax(0, 1fr))
            }

            .lg\:px-8 {
                padding-left: 2rem;
                padding-right: 2rem
            }
        }

        .\[\&_svg\]\:pointer-events-none svg {
            pointer-events: none
        }

        .\[\&_svg\]\:size-4 svg {
            width: 1rem;
            height: 1rem
        }

        .\[\&_svg\]\:shrink-0 svg {
            flex-shrink: 0
        }
    </style>
    <style>
        @tailwind base;
        @tailwind components;
        @tailwind utilities;

        *,
        ::before,
        ::after {
            --tw-border-spacing-x: 0;
            --tw-border-spacing-y: 0;
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-rotate: 0;
            --tw-skew-x: 0;
            --tw-skew-y: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-pan-x: ;
            --tw-pan-y: ;
            --tw-pinch-zoom: ;
            --tw-scroll-snap-strictness: proximity;
            --tw-gradient-from-position: ;
            --tw-gradient-via-position: ;
            --tw-gradient-to-position: ;
            --tw-ordinal: ;
            --tw-slashed-zero: ;
            --tw-numeric-figure: ;
            --tw-numeric-spacing: ;
            --tw-numeric-fraction: ;
            --tw-ring-inset: ;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgb(59 130 246 / 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            --tw-blur: ;
            --tw-brightness: ;
            --tw-contrast: ;
            --tw-grayscale: ;
            --tw-hue-rotate: ;
            --tw-invert: ;
            --tw-saturate: ;
            --tw-sepia: ;
            --tw-drop-shadow: ;
            --tw-backdrop-blur: ;
            --tw-backdrop-brightness: ;
            --tw-backdrop-contrast: ;
            --tw-backdrop-grayscale: ;
            --tw-backdrop-hue-rotate: ;
            --tw-backdrop-invert: ;
            --tw-backdrop-opacity: ;
            --tw-backdrop-saturate: ;
            --tw-backdrop-sepia: ;
            --tw-contain-size: ;
            --tw-contain-layout: ;
            --tw-contain-paint: ;
            --tw-contain-style:
        }

        ::backdrop {
            --tw-border-spacing-x: 0;
            --tw-border-spacing-y: 0;
            --tw-translate-x: 0;
            --tw-translate-y: 0;
            --tw-rotate: 0;
            --tw-skew-x: 0;
            --tw-skew-y: 0;
            --tw-scale-x: 1;
            --tw-scale-y: 1;
            --tw-pan-x: ;
            --tw-pan-y: ;
            --tw-pinch-zoom: ;
            --tw-scroll-snap-strictness: proximity;
            --tw-gradient-from-position: ;
            --tw-gradient-via-position: ;
            --tw-gradient-to-position: ;
            --tw-ordinal: ;
            --tw-slashed-zero: ;
            --tw-numeric-figure: ;
            --tw-numeric-spacing: ;
            --tw-numeric-fraction: ;
            --tw-ring-inset: ;
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgb(59 130 246 / 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            --tw-blur: ;
            --tw-brightness: ;
            --tw-contrast: ;
            --tw-grayscale: ;
            --tw-hue-rotate: ;
            --tw-invert: ;
            --tw-saturate: ;
            --tw-sepia: ;
            --tw-drop-shadow: ;
            --tw-backdrop-blur: ;
            --tw-backdrop-brightness: ;
            --tw-backdrop-contrast: ;
            --tw-backdrop-grayscale: ;
            --tw-backdrop-hue-rotate: ;
            --tw-backdrop-invert: ;
            --tw-backdrop-opacity: ;
            --tw-backdrop-saturate: ;
            --tw-backdrop-sepia: ;
            --tw-contain-size: ;
            --tw-contain-layout: ;
            --tw-contain-paint: ;
            --tw-contain-style:
        }

        /* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */
        *,
        ::after,
        ::before {
            box-sizing: border-box;
            border-width: 0;
            border-style: solid;
            border-color: #e5e7eb
        }

        ::after,
        ::before {
            --tw-content: ''
        }

        :host,
        html {
            line-height: 1.5;
            -webkit-text-size-adjust: 100%;
            -moz-tab-size: 4;
            tab-size: 4;
            font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-feature-settings: normal;
            font-variation-settings: normal;
            -webkit-tap-highlight-color: transparent
        }

        body {
            margin: 0;
            line-height: inherit
        }

        hr {
            height: 0;
            color: inherit;
            border-top-width: 1px
        }

        abbr:where([title]) {
            -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-size: inherit;
            font-weight: inherit
        }

        a {
            color: inherit;
            text-decoration: inherit
        }

        b,
        strong {
            font-weight: bolder
        }

        code,
        kbd,
        pre,
        samp {
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-feature-settings: normal;
            font-variation-settings: normal;
            font-size: 1em
        }

        small {
            font-size: 80%
        }

        sub,
        sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline
        }

        sub {
            bottom: -.25em
        }

        sup {
            top: -.5em
        }

        table {
            text-indent: 0;
            border-color: inherit;
            border-collapse: collapse
        }

        button,
        input,
        optgroup,
        select,
        textarea {
            font-family: inherit;
            font-feature-settings: inherit;
            font-variation-settings: inherit;
            font-size: 100%;
            font-weight: inherit;
            line-height: inherit;
            letter-spacing: inherit;
            color: inherit;
            margin: 0;
            padding: 0
        }

        button,
        select {
            text-transform: none
        }

        button,
        input:where([type=button]),
        input:where([type=reset]),
        input:where([type=submit]) {
            -webkit-appearance: button;
            background-color: transparent;
            background-image: none
        }

        :-moz-focusring {
            outline: auto
        }

        :-moz-ui-invalid {
            box-shadow: none
        }

        progress {
            vertical-align: baseline
        }

        ::-webkit-inner-spin-button,
        ::-webkit-outer-spin-button {
            height: auto
        }

        [type=search] {
            -webkit-appearance: textfield;
            outline-offset: -2px
        }

        ::-webkit-search-decoration {
            -webkit-appearance: none
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit
        }

        summary {
            display: list-item
        }

        blockquote,
        dd,
        dl,
        figure,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        hr,
        p,
        pre {
            margin: 0
        }

        fieldset {
            margin: 0;
            padding: 0
        }

        legend {
            padding: 0
        }

        menu,
        ol,
        ul {
            list-style: none;
            margin: 0;
            padding: 0
        }

        dialog {
            padding: 0
        }

        textarea {
            resize: vertical
        }

        input::placeholder,
        textarea::placeholder {
            opacity: 1;
            color: #9ca3af
        }

        [role=button],
        button {
            cursor: pointer
        }

        :disabled {
            cursor: default
        }

        audio,
        canvas,
        embed,
        iframe,
        img,
        object,
        svg,
        video {
            display: block;
            vertical-align: middle
        }

        img,
        video {
            max-width: 100%;
            height: auto
        }

        [hidden]:where(:not([hidden=until-found])) {
            display: none
        }

        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 221.2 83.2% 53.3%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 221.2 83.2% 53.3%;
            --radius: .5rem
        }

        .dark {
            --background: 220 13% 13%;
            --foreground: 210 40% 98%;
            --card: 220 13% 18%;
            --card-foreground: 210 40% 98%;
            --popover: 220 13% 18%;
            --popover-foreground: 210 40% 98%;
            --primary: 140 60% 40%;
            --primary-foreground: 210 40% 98%;
            --secondary: 220 13% 25%;
            --secondary-foreground: 210 40% 98%;
            --muted: 220 13% 20%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 220 13% 25%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 220 13% 25%;
            --input: 220 13% 20%;
            --ring: 140 60% 40%
        }

        * {
            border-color: hsl(var(--border))
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground))
        }

        .fixed {
            position: fixed
        }

        .absolute {
            position: absolute
        }

        .relative {
            position: relative
        }

        .top-0 {
            top: 0px
        }

        .bottom-4 {
            bottom: 1rem
        }

        .left-4 {
            left: 1rem
        }

        .z-50 {
            z-index: 50
        }

        .-mx-1 {
            margin-left: -0.25rem;
            margin-right: -0.25rem
        }

        .my-1 {
            margin-top: 0.25rem;
            margin-bottom: 0.25rem
        }

        .mb-4 {
            margin-bottom: 1rem
        }

        .mr-3 {
            margin-right: 0.75rem
        }

        .mb-1 {
            margin-bottom: 0.25rem
        }

        .mb-2 {
            margin-bottom: 0.5rem
        }

        .mr-2 {
            margin-right: 0.5rem
        }

        .mt-4 {
            margin-top: 1rem
        }

        .mt-6 {
            margin-top: 1.5rem
        }

        .flex {
            display: flex
        }

        .inline-flex {
            display: inline-flex
        }

        .grid {
            display: grid
        }

        .hidden {
            display: none
        }

        .h-32 {
            height: 8rem
        }

        .h-10 {
            height: 2.5rem
        }

        .h-16 {
            height: 4rem
        }

        .h-2 {
            height: 0.5rem
        }

        .h-4 {
            height: 1rem
        }

        .h-5 {
            height: 1.25rem
        }

        .h-6 {
            height: 1.5rem
        }

        .h-64 {
            height: 16rem
        }

        .h-8 {
            height: 2rem
        }

        .h-auto {
            height: auto
        }

        .h-full {
            height: 100%
        }

        .h-screen {
            height: 100vh
        }

        .h-3 {
            height: 0.75rem
        }

        .h-9 {
            height: 2.25rem
        }

        .h-\[200px\] {
            height: 200px
        }

        .h-px {
            height: 1px
        }

        .h-12 {
            height: 3rem
        }

        .max-h-screen {
            max-height: 100vh
        }

        .min-h-screen {
            min-height: 100vh
        }

        .min-h-\[80px\] {
            min-height: 80px
        }

        .w-32 {
            width: 8rem
        }

        .w-full {
            width: 100%
        }

        .w-10 {
            width: 2.5rem
        }

        .w-2 {
            width: 0.5rem
        }

        .w-4 {
            width: 1rem
        }

        .w-5 {
            width: 1.25rem
        }

        .w-6 {
            width: 1.5rem
        }

        .w-8 {
            width: 2rem
        }

        .w-3 {
            width: 0.75rem
        }

        .w-56 {
            width: 14rem
        }

        .w-12 {
            width: 3rem
        }

        .min-w-32 {
            min-width: 8rem
        }

        .max-w-md {
            max-width: 28rem
        }

        .flex-1 {
            flex: 1 1 0%
        }

        .shrink-0 {
            flex-shrink: 0
        }

        @keyframes spin {
            to {
                transform: rotate(360deg)
            }
        }

        .animate-spin {
            animation: spin 1s linear infinite
        }

        .cursor-default {
            cursor: default
        }

        .select-none {
            -webkit-user-select: none;
            user-select: none
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr))
        }

        .flex-row {
            flex-direction: row
        }

        .flex-col {
            flex-direction: column
        }

        .flex-col-reverse {
            flex-direction: column-reverse
        }

        .items-center {
            align-items: center
        }

        .justify-start {
            justify-content: flex-start
        }

        .justify-end {
            justify-content: flex-end
        }

        .justify-center {
            justify-content: center
        }

        .justify-between {
            justify-content: space-between
        }

        .gap-2 {
            gap: 0.5rem
        }

        .gap-4 {
            gap: 1rem
        }

        .gap-6 {
            gap: 1.5rem
        }

        .space-x-2> :not([hidden])~ :not([hidden]) {
            --tw-space-x-reverse: 0;
            margin-right: calc(0.5rem * var(--tw-space-x-reverse));
            margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))
        }

        .space-x-3> :not([hidden])~ :not([hidden]) {
            --tw-space-x-reverse: 0;
            margin-right: calc(0.75rem * var(--tw-space-x-reverse));
            margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))
        }

        .space-x-4> :not([hidden])~ :not([hidden]) {
            --tw-space-x-reverse: 0;
            margin-right: calc(1rem * var(--tw-space-x-reverse));
            margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
        }

        .space-y-0\.5> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.125rem * var(--tw-space-y-reverse))
        }

        .space-y-1\.5> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.375rem * var(--tw-space-y-reverse))
        }

        .space-y-2> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.5rem * var(--tw-space-y-reverse))
        }

        .space-y-3> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.75rem * var(--tw-space-y-reverse))
        }

        .space-y-4> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(1rem * var(--tw-space-y-reverse))
        }

        .space-y-6> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(1.5rem * var(--tw-space-y-reverse))
        }

        .space-y-1> :not([hidden])~ :not([hidden]) {
            --tw-space-y-reverse: 0;
            margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
            margin-bottom: calc(0.25rem * var(--tw-space-y-reverse))
        }

        .overflow-hidden {
            overflow: hidden
        }

        .overflow-x-auto {
            overflow-x: auto
        }

        .overflow-y-auto {
            overflow-y: auto
        }

        .whitespace-nowrap {
            white-space: nowrap
        }

        .rounded-full {
            border-radius: 9999px
        }

        .rounded-md {
            border-radius: calc(var(--radius) - 2px)
        }

        .rounded {
            border-radius: 0.25rem
        }

        .rounded-lg {
            border-radius: var(--radius)
        }

        .rounded-sm {
            border-radius: calc(var(--radius) - 4px)
        }

        .border {
            border-width: 1px
        }

        .border-b-2 {
            border-bottom-width: 2px
        }

        .border-b {
            border-bottom-width: 1px
        }

        .border-t {
            border-top-width: 1px
        }

        .border-primary {
            border-color: hsl(var(--primary))
        }

        .border-slate-600 {
            --tw-border-opacity: 1;
            border-color: rgb(71 85 105 / var(--tw-border-opacity, 1))
        }

        .border-slate-700 {
            --tw-border-opacity: 1;
            border-color: rgb(51 65 85 / var(--tw-border-opacity, 1))
        }

        .border-border {
            border-color: hsl(var(--border))
        }

        .border-green-500 {
            --tw-border-opacity: 1;
            border-color: rgb(34 197 94 / var(--tw-border-opacity, 1))
        }

        .border-input {
            border-color: hsl(var(--input))
        }

        .border-transparent {
            border-color: transparent
        }

        .border-yellow-600 {
            --tw-border-opacity: 1;
            border-color: rgb(202 138 4 / var(--tw-border-opacity, 1))
        }

        .border-gray-200 {
            --tw-border-opacity: 1;
            border-color: rgb(229 231 235 / var(--tw-border-opacity, 1))
        }

        .border-gray-300 {
            --tw-border-opacity: 1;
            border-color: rgb(209 213 219 / var(--tw-border-opacity, 1))
        }

        .bg-background {
            background-color: hsl(var(--background))
        }

        .bg-green-500 {
            --tw-bg-opacity: 1;
            background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1))
        }

        .bg-slate-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1))
        }

        .bg-slate-700 {
            --tw-bg-opacity: 1;
            background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1))
        }

        .bg-slate-800 {
            --tw-bg-opacity: 1;
            background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1))
        }

        .bg-card {
            background-color: hsl(var(--card))
        }

        .bg-gray-800 {
            --tw-bg-opacity: 1;
            background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1))
        }

        .bg-primary {
            background-color: hsl(var(--primary))
        }

        .bg-red-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1))
        }

        .bg-secondary {
            background-color: hsl(var(--secondary))
        }

        .bg-transparent {
            background-color: transparent
        }

        .bg-yellow-600 {
            --tw-bg-opacity: 1;
            background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1))
        }

        .bg-yellow-900\/20 {
            background-color: rgb(113 63 18 / 0.2)
        }

        .bg-input {
            background-color: hsl(var(--input))
        }

        .bg-muted {
            background-color: hsl(var(--muted))
        }

        .bg-popover {
            background-color: hsl(var(--popover))
        }

        .bg-\[\#1a4d3a\] {
            --tw-bg-opacity: 1;
            background-color: rgb(26 77 58 / var(--tw-bg-opacity, 1))
        }

        .bg-gray-50 {
            --tw-bg-opacity: 1;
            background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1))
        }

        .bg-white {
            --tw-bg-opacity: 1;
            background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1))
        }

        .p-4 {
            padding: 1rem
        }

        .p-6 {
            padding: 1.5rem
        }

        .p-3 {
            padding: 0.75rem
        }

        .p-1 {
            padding: 0.25rem
        }

        .px-2 {
            padding-left: 0.5rem;
            padding-right: 0.5rem
        }

        .px-3 {
            padding-left: 0.75rem;
            padding-right: 0.75rem
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem
        }

        .px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem
        }

        .py-1\.5 {
            padding-top: 0.375rem;
            padding-bottom: 0.375rem
        }

        .py-2 {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem
        }

        .py-3 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem
        }

        .py-6 {
            padding-top: 1.5rem;
            padding-bottom: 1.5rem
        }

        .px-2\.5 {
            padding-left: 0.625rem;
            padding-right: 0.625rem
        }

        .py-0\.5 {
            padding-top: 0.125rem;
            padding-bottom: 0.125rem
        }

        .py-2\.5 {
            padding-top: 0.625rem;
            padding-bottom: 0.625rem
        }

        .py-12 {
            padding-top: 3rem;
            padding-bottom: 3rem
        }

        .pt-0 {
            padding-top: 0px
        }

        .pb-6 {
            padding-bottom: 1.5rem
        }

        .text-left {
            text-align: left
        }

        .text-center {
            text-align: center
        }

        .text-right {
            text-align: right
        }

        .text-lg {
            font-size: 1.125rem;
            line-height: 1.75rem
        }

        .text-sm {
            font-size: 0.875rem;
            line-height: 1.25rem
        }

        .text-xs {
            font-size: 0.75rem;
            line-height: 1rem
        }

        .text-2xl {
            font-size: 1.5rem;
            line-height: 2rem
        }

        .text-3xl {
            font-size: 1.875rem;
            line-height: 2.25rem
        }

        .text-xl {
            font-size: 1.25rem;
            line-height: 1.75rem
        }

        .font-bold {
            font-weight: 700
        }

        .font-medium {
            font-weight: 500
        }

        .font-semibold {
            font-weight: 600
        }

        .font-normal {
            font-weight: 400
        }

        .uppercase {
            text-transform: uppercase
        }

        .leading-none {
            line-height: 1
        }

        .tracking-wider {
            letter-spacing: 0.05em
        }

        .tracking-tight {
            letter-spacing: -0.025em
        }

        .text-gray-400 {
            --tw-text-opacity: 1;
            color: rgb(156 163 175 / var(--tw-text-opacity, 1))
        }

        .text-white {
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1))
        }

        .text-card-foreground {
            color: hsl(var(--card-foreground))
        }

        .text-green-400 {
            --tw-text-opacity: 1;
            color: rgb(74 222 128 / var(--tw-text-opacity, 1))
        }

        .text-muted-foreground {
            color: hsl(var(--muted-foreground))
        }

        .text-red-500 {
            --tw-text-opacity: 1;
            color: rgb(239 68 68 / var(--tw-text-opacity, 1))
        }

        .text-yellow-500 {
            --tw-text-opacity: 1;
            color: rgb(234 179 8 / var(--tw-text-opacity, 1))
        }

        .text-foreground {
            color: hsl(var(--foreground))
        }

        .text-popover-foreground {
            color: hsl(var(--popover-foreground))
        }

        .text-gray-600 {
            --tw-text-opacity: 1;
            color: rgb(75 85 99 / var(--tw-text-opacity, 1))
        }

        .text-gray-700 {
            --tw-text-opacity: 1;
            color: rgb(55 65 81 / var(--tw-text-opacity, 1))
        }

        .text-gray-800 {
            --tw-text-opacity: 1;
            color: rgb(31 41 55 / var(--tw-text-opacity, 1))
        }

        .text-gray-900 {
            --tw-text-opacity: 1;
            color: rgb(17 24 39 / var(--tw-text-opacity, 1))
        }

        .text-\[\#1a4d3a\] {
            --tw-text-opacity: 1;
            color: rgb(26 77 58 / var(--tw-text-opacity, 1))
        }

        .text-black {
            --tw-text-opacity: 1;
            color: rgb(0 0 0 / var(--tw-text-opacity, 1))
        }

        .text-gray-500 {
            --tw-text-opacity: 1;
            color: rgb(107 114 128 / var(--tw-text-opacity, 1))
        }

        .placeholder-gray-500::placeholder {
            --tw-placeholder-opacity: 1;
            color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1))
        }

        .shadow-md {
            --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
        }

        .shadow-lg {
            --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
        }

        .shadow-xl {
            --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
        }

        .ring-offset-background {
            --tw-ring-offset-color: hsl(var(--background))
        }

        .transition-all {
            transition-property: all;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms
        }

        .transition-colors {
            transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms
        }

        .duration-300 {
            transition-duration: 300ms
        }

        .ease-in-out {
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
        }

        @keyframes enter {
            from {
                opacity: var(--tw-enter-opacity, 1);
                transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))
            }
        }

        @keyframes exit {
            to {
                opacity: var(--tw-exit-opacity, 1);
                transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))
            }
        }

        .duration-300 {
            animation-duration: 300ms
        }

        .ease-in-out {
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
        }

        .file\:border-0::file-selector-button {
            border-width: 0px
        }

        .file\:bg-transparent::file-selector-button {
            background-color: transparent
        }

        .file\:text-sm::file-selector-button {
            font-size: 0.875rem;
            line-height: 1.25rem
        }

        .file\:font-medium::file-selector-button {
            font-weight: 500
        }

        .file\:text-foreground::file-selector-button {
            color: hsl(var(--foreground))
        }

        .placeholder\:text-muted-foreground::placeholder {
            color: hsl(var(--muted-foreground))
        }

        .placeholder\:text-gray-400::placeholder {
            --tw-text-opacity: 1;
            color: rgb(156 163 175 / var(--tw-text-opacity, 1))
        }

        .last\:border-b-0:last-child {
            border-bottom-width: 0px
        }

        .hover\:bg-slate-700:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1))
        }

        .hover\:bg-accent:hover {
            background-color: hsl(var(--accent))
        }

        .hover\:bg-green-500:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1))
        }

        .hover\:bg-green-600:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1))
        }

        .hover\:bg-primary\/80:hover {
            background-color: hsl(var(--primary) / 0.8)
        }

        .hover\:bg-secondary\/80:hover {
            background-color: hsl(var(--secondary) / 0.8)
        }

        .hover\:bg-\[\#1a4d3a\]\/90:hover {
            background-color: rgb(26 77 58 / 0.9)
        }

        .hover\:text-accent-foreground:hover {
            color: hsl(var(--accent-foreground))
        }

        .hover\:text-white:hover {
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1))
        }

        .hover\:underline:hover {
            -webkit-text-decoration-line: underline;
            text-decoration-line: underline
        }

        .focus\:border-gray-800:focus {
            --tw-border-opacity: 1;
            border-color: rgb(31 41 55 / var(--tw-border-opacity, 1))
        }

        .focus\:border-\[\#1a4d3a\]:focus {
            --tw-border-opacity: 1;
            border-color: rgb(26 77 58 / var(--tw-border-opacity, 1))
        }

        .focus\:bg-accent:focus {
            background-color: hsl(var(--accent))
        }

        .focus\:text-accent-foreground:focus {
            color: hsl(var(--accent-foreground))
        }

        .focus\:ring-2:focus {
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
        }

        .focus\:ring-ring:focus {
            --tw-ring-color: hsl(var(--ring))
        }

        .focus\:ring-gray-800:focus {
            --tw-ring-opacity: 1;
            --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity, 1))
        }

        .focus\:ring-\[\#1a4d3a\]:focus {
            --tw-ring-opacity: 1;
            --tw-ring-color: rgb(26 77 58 / var(--tw-ring-opacity, 1))
        }

        .focus\:ring-offset-2:focus {
            --tw-ring-offset-width: 2px
        }

        .focus-visible\:ring-2:focus-visible {
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
        }

        .focus-visible\:ring-ring:focus-visible {
            --tw-ring-color: hsl(var(--ring))
        }

        .focus-visible\:ring-offset-2:focus-visible {
            --tw-ring-offset-width: 2px
        }

        .disabled\:pointer-events-none:disabled {
            pointer-events: none
        }

        .disabled\:cursor-not-allowed:disabled {
            cursor: not-allowed
        }

        .disabled\:opacity-50:disabled {
            opacity: 0.5
        }

        .peer:disabled~.peer-disabled\:cursor-not-allowed {
            cursor: not-allowed
        }

        .peer:disabled~.peer-disabled\:opacity-70{opacity:0.7}.data-\[state\=open\]\:animate-in[data-state="open"] {
            animation-name: enter;
            animation-duration: 150ms;
            --tw-enter-opacity: initial;
            --tw-enter-scale: initial;
            --tw-enter-rotate: initial;
            --tw-enter-translate-x: initial;
            --tw-enter-translate-y: initial
        }

        .data-\[state\=closed\]\:animate-out[data-state="closed"] {
            animation-name: exit;
            animation-duration: 150ms;
            --tw-exit-opacity: initial;
            --tw-exit-scale: initial;
            --tw-exit-rotate: initial;
            --tw-exit-translate-x: initial;
            --tw-exit-translate-y: initial
        }

        .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
            --tw-exit-opacity: 0
        }

        .data-\[state\=open\]\:fade-in-0[data-state="open"] {
            --tw-enter-opacity: 0
        }

        .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
            --tw-exit-scale: .95
        }

        .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
            --tw-enter-scale: .95
        }

        .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
            --tw-enter-translate-y: -0.5rem
        }

        .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
            --tw-enter-translate-x: 0.5rem
        }

        .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
            --tw-enter-translate-x: -0.5rem
        }

        .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
            --tw-enter-translate-y: 0.5rem
        }

        @media (min-width: 640px) {
            .sm\:bottom-0 {
                bottom: 0px
            }

            .sm\:right-0 {
                right: 0px
            }

            .sm\:top-auto {
                top: auto
            }

            .sm\:flex-col {
                flex-direction: column
            }

            .sm\:px-6 {
                padding-left: 1.5rem;
                padding-right: 1.5rem
            }
        }

        @media (min-width: 768px) {
            .md\:col-span-2 {
                grid-column: span 2 / span 2
            }

            .md\:flex {
                display: flex
            }

            .md\:hidden {
                display: none
            }

            .md\:w-56 {
                width: 14rem
            }

            .md\:max-w-\[420px\] {
                max-width: 420px
            }

            .md\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr))
            }

            .md\:grid-cols-6 {
                grid-template-columns: repeat(6, minmax(0, 1fr))
            }

            .md\:flex-col {
                flex-direction: column
            }
        }

        @media (min-width: 1024px) {
            .lg\:col-span-2 {
                grid-column: span 2 / span 2
            }

            .lg\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr))
            }

            .lg\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr))
            }

            .lg\:grid-cols-5 {
                grid-template-columns: repeat(5, minmax(0, 1fr))
            }

            .lg\:px-8 {
                padding-left: 2rem;
                padding-right: 2rem
            }
        }

        .\[\&_svg\]\:pointer-events-none svg {
            pointer-events: none
        }

        .\[\&_svg\]\:size-4 svg {
            width: 1rem;
            height: 1rem
        }

        .\[\&_svg\]\:shrink-0 svg {
            flex-shrink: 0
        }
    </style>
    <style id="font-changer-styles">
        * {
            font-family: !important;


        }
    </style>
    <link class="tempLink" rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=">
    <style type="text/tailwindcss">
        @tailwind base;
        @tailwind components;
        @tailwind utilities;

        @layer base {
            :root {
                --background: 0 0% 100%;
                --foreground: 222.2 84% 4.9%;
                --card: 0 0% 100%;
                --card-foreground: 222.2 84% 4.9%;
                --popover: 0 0% 100%;
                --popover-foreground: 222.2 84% 4.9%;
                --primary: 221.2 83.2% 53.3%;
                --primary-foreground: 210 40% 98%;
                --secondary: 210 40% 96%;
                --secondary-foreground: 222.2 84% 4.9%;
                --muted: 210 40% 96%;
                --muted-foreground: 215.4 16.3% 46.9%;
                --accent: 210 40% 96%;
                --accent-foreground: 222.2 84% 4.9%;
                --destructive: 0 84.2% 60.2%;
                --destructive-foreground: 210 40% 98%;
                --border: 214.3 31.8% 91.4%;
                --input: 214.3 31.8% 91.4%;
                --ring: 221.2 83.2% 53.3%;
                --radius: .5rem
            }

            .dark {
                --background: 220 13% 13%;
                --foreground: 210 40% 98%;
                --card: 220 13% 18%;
                --card-foreground: 210 40% 98%;
                --popover: 220 13% 18%;
                --popover-foreground: 210 40% 98%;
                --primary: 140 60% 40%;
                --primary-foreground: 210 40% 98%;
                --secondary: 220 13% 25%;
                --secondary-foreground: 210 40% 98%;
                --muted: 220 13% 20%;
                --muted-foreground: 215 20.2% 65.1%;
                --accent: 220 13% 25%;
                --accent-foreground: 210 40% 98%;
                --destructive: 0 62.8% 30.6%;
                --destructive-foreground: 210 40% 98%;
                --border: 220 13% 25%;
                --input: 220 13% 20%;
                --ring: 140 60% 40%
            }

            * {
                @apply border-border;
            }

            body {
                @apply bg-background text-foreground;
            }
        }
    </style>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
        rel="stylesheet">
    <style>
        .f_SW50ZXI {
            font-family: 'Inter';
        }
    </style>
</head>

<body class="f_SW50ZXI" style="">
    <div class="min-h-screen flex items-center justify-center bg-white py-12 px-4 sm:px-6 lg:px-8">
        <div class="rounded-lg border shadow-2xs w-full max-w-md bg-white text-black border-gray-200" data-v0-t="card">
            <div class="flex flex-col p-6 space-y-1 text-center">
                <div class="flex items-center justify-center mb-4 gap-2">
                    <div class="w-12 h-12 bg-[#1a4d3a] rounded-md flex items-center justify-center"><span
                            class="text-white text-2xl font-bold">P</span></div><span
                        class="text-2xl font-bold text-black">Pak Funding</span>
                </div>
                <h3 class="tracking-tight text-2xl font-bold text-black">Create Account</h3>
                <p class="text-sm text-gray-500">Join our trading platform</p>
            </div>
            <div class="p-6 pt-0">
                <form class="space-y-4">
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="name">Full Name</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="name" placeholder="Enter your full name" required="" type="text"
                            value=""></div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="email">Email</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="email" placeholder="Enter your email" required="" type="email" value="">
                    </div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="password">Password</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="password" placeholder="Create a password" required="" type="password"
                            value=""></div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="confirmPassword">Confirm Password</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="confirmPassword" placeholder="Confirm your password" required="" type="password"
                            value=""></div><button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 w-full bg-[#1a4d3a] text-white hover:bg-[#1a4d3a]/90 mt-6"
                        type="submit">Create Account</button>
                </form>
                <div class="mt-4 text-center text-sm text-gray-600">Already have an account? <a href="/auth/login"
                        class="text-[#1a4d3a] hover:underline font-medium">Sign in</a></div>
            </div>
        </div>
    </div>
</body>

</html>
