<!DOCTYPE html>
<html lang="en" class="dark">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Pak Funding - Sign up</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
</head>

<body class="f_SW50ZXI">
    <div class="min-h-screen flex items-center justify-center bg-white py-12 px-4 sm:px-6 lg:px-8">
        <div class="rounded-lg border shadow-2xs w-full max-w-md bg-white text-black border-gray-200" data-v0-t="card">
            <div class="flex flex-col p-6 space-y-1 text-center">
                <div class="flex items-center justify-center mb-4 gap-2">
                    <div class="w-12 h-12 bg-[#1a4d3a] rounded-md flex items-center justify-center">
                        <span class="text-white text-2xl font-bold">P</span>
                    </div>
                    <span class="text-2xl font-bold text-black">Pak Funding</span>
                </div>
                <h3 class="tracking-tight text-2xl font-bold text-black">Create Account</h3>
                <p class="text-sm text-gray-500">Join our trading platform and start your journey</p>
            </div>
            <div class="p-6 pt-0">
                <!-- Display validation errors -->
                <?php if($errors->any()): ?>
                    <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    There were errors with your submission
                                </h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo e(route('register')); ?>" class="space-y-4">
                    <?php echo csrf_field(); ?>

                    <!-- Full Name -->
                    <div class="space-y-2">
                        <label class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium" for="name">
                            Full Name
                        </label>
                        <input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a] <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 focus:ring-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            id="name"
                            name="name"
                            placeholder="Enter your full name"
                            required
                            type="text"
                            value="<?php echo e(old('name')); ?>"
                            autofocus>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <!-- Email -->
                    <div class="space-y-2">
                        <label class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium" for="email">
                            Email
                        </label>
                        <input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a] <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 focus:ring-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            id="email"
                            name="email"
                            placeholder="Enter your email"
                            required
                            type="email"
                            value="<?php echo e(old('email')); ?>">
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <!-- Password -->
                    <div class="space-y-2">
                        <label class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium" for="password">
                            Password
                        </label>
                        <input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a] <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 focus:ring-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            id="password"
                            name="password"
                            placeholder="Create a password"
                            required
                            type="password">
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                        <!-- Password strength indicator -->
                        <div id="password-strength" class="hidden mt-2">
                            <div class="flex space-x-1">
                                <div class="h-1 w-full bg-gray-200 rounded-full">
                                    <div id="strength-bar" class="h-1 bg-red-500 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                            </div>
                            <p id="strength-text" class="text-xs mt-1 text-gray-500">Password strength: Weak</p>
                        </div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="space-y-2">
                        <label class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium" for="password_confirmation">
                            Confirm Password
                        </label>
                        <input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a] <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 focus:border-red-500 focus:ring-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            id="password_confirmation"
                            name="password_confirmation"
                            placeholder="Confirm your password"
                            required
                            type="password">
                        <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>                    <!-- Submit Button -->
                    <button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 w-full bg-[#1a4d3a] text-white hover:bg-[#1a4d3a]/90 mt-6"
                        type="submit"
                        id="register-button">
                        <span id="button-text">Create Account</span>
                        <svg id="loading-spinner" class="hidden animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </form>

                <!-- Sign in link -->
                <div class="mt-4 text-center text-sm text-gray-600">
                    Already have an account?
                    <a href="<?php echo e(route('login')); ?>" class="text-[#1a4d3a] hover:underline font-medium">
                        Sign in
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitButton = document.getElementById('register-button');
            const buttonText = document.getElementById('button-text');
            const loadingSpinner = document.getElementById('loading-spinner');
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('password_confirmation');

            // Form submission handling
            if (form && submitButton) {
                form.addEventListener('submit', function(e) {
                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.textContent = 'Creating Account...';
                    loadingSpinner.classList.remove('hidden');
                });
            }

            // Password strength and confirmation validation
            if (confirmPasswordField && passwordField) {
                const strengthIndicator = document.getElementById('password-strength');
                const strengthBar = document.getElementById('strength-bar');
                const strengthText = document.getElementById('strength-text');

                // Password strength checking
                passwordField.addEventListener('input', function() {
                    const password = passwordField.value;
                    const confirmPassword = confirmPasswordField.value;

                    // Show/hide strength indicator
                    if (password.length > 0) {
                        strengthIndicator.classList.remove('hidden');

                        // Calculate password strength
                        const strength = calculatePasswordStrength(password);
                        updatePasswordStrength(strength, strengthBar, strengthText);
                    } else {
                        strengthIndicator.classList.add('hidden');
                    }

                    // Check password confirmation
                    if (confirmPassword && password !== confirmPassword) {
                        confirmPasswordField.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPasswordField.setCustomValidity('');
                    }
                });

                // Password confirmation validation
                confirmPasswordField.addEventListener('input', function() {
                    const password = passwordField.value;
                    const confirmPassword = confirmPasswordField.value;

                    if (confirmPassword && password !== confirmPassword) {
                        confirmPasswordField.setCustomValidity('Passwords do not match');
                        confirmPasswordField.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
                        confirmPasswordField.classList.remove('border-gray-300', 'focus:border-[#1a4d3a]', 'focus:ring-[#1a4d3a]');
                    } else {
                        confirmPasswordField.setCustomValidity('');
                        confirmPasswordField.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
                        confirmPasswordField.classList.add('border-gray-300', 'focus:border-[#1a4d3a]', 'focus:ring-[#1a4d3a]');
                    }
                });
            }

            // Password strength calculation
            function calculatePasswordStrength(password) {
                let score = 0;

                // Length check
                if (password.length >= 8) score += 25;
                if (password.length >= 12) score += 25;

                // Character variety checks
                if (/[a-z]/.test(password)) score += 10;
                if (/[A-Z]/.test(password)) score += 10;
                if (/[0-9]/.test(password)) score += 15;
                if (/[^A-Za-z0-9]/.test(password)) score += 15;

                return Math.min(score, 100);
            }

            // Update password strength indicator
            function updatePasswordStrength(strength, strengthBar, strengthText) {
                strengthBar.style.width = strength + '%';

                if (strength < 30) {
                    strengthBar.className = 'h-1 bg-red-500 rounded-full transition-all duration-300';
                    strengthText.textContent = 'Password strength: Weak';
                    strengthText.className = 'text-xs mt-1 text-red-500';
                } else if (strength < 60) {
                    strengthBar.className = 'h-1 bg-yellow-500 rounded-full transition-all duration-300';
                    strengthText.textContent = 'Password strength: Fair';
                    strengthText.className = 'text-xs mt-1 text-yellow-600';
                } else if (strength < 80) {
                    strengthBar.className = 'h-1 bg-blue-500 rounded-full transition-all duration-300';
                    strengthText.textContent = 'Password strength: Good';
                    strengthText.className = 'text-xs mt-1 text-blue-600';
                } else {
                    strengthBar.className = 'h-1 bg-green-500 rounded-full transition-all duration-300';
                    strengthText.textContent = 'Password strength: Strong';
                    strengthText.className = 'text-xs mt-1 text-green-600';
                }
            }

            // Auto-hide error messages after 5 seconds
            const errorMessages = document.querySelectorAll('.text-red-500');
            errorMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0.7';
                }, 5000);
            });

            // Focus management
            const nameField = document.getElementById('name');
            if (nameField && !nameField.value) {
                nameField.focus();
            }
        });

        // Utility function to show notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${
                type === 'success' ? 'bg-green-600' :
                type === 'error' ? 'bg-red-600' :
                'bg-blue-600'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 4000);
        }
    </script>
</body>

</html>
<?php /**PATH D:\project\resources\views/auth/register.blade.php ENDPATH**/ ?>