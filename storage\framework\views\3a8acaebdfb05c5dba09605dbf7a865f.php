<html lang="en" class="dark">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Pak Funding - Sign up</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="f_SW50ZXI" style="">
    <div class="min-h-screen flex items-center justify-center bg-white py-12 px-4 sm:px-6 lg:px-8">
        <div class="rounded-lg border shadow-2xs w-full max-w-md bg-white text-black border-gray-200" data-v0-t="card">
            <div class="flex flex-col p-6 space-y-1 text-center">
                <div class="flex items-center justify-center mb-4 gap-2">
                    <div class="w-12 h-12 bg-[#1a4d3a] rounded-md flex items-center justify-center"><span
                            class="text-white text-2xl font-bold">P</span></div><span
                        class="text-2xl font-bold text-black">Pak Funding</span>
                </div>
                <h3 class="tracking-tight text-2xl font-bold text-black">Create Account</h3>
                <p class="text-sm text-gray-500">Join our trading platform</p>
            </div>
            <div class="p-6 pt-0">
                <form class="space-y-4">
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="name">Full Name</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="name" placeholder="Enter your full name" required="" type="text"
                            value=""></div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="email">Email</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="email" placeholder="Enter your email" required="" type="email" value="">
                    </div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="password">Password</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="password" placeholder="Create a password" required="" type="password"
                            value=""></div>
                    <div class="space-y-2"><label
                            class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black font-medium"
                            for="confirmPassword">Confirm Password</label><input
                            class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-gray-300 text-black placeholder:text-gray-400 focus:border-[#1a4d3a] focus:ring-[#1a4d3a]"
                            id="confirmPassword" placeholder="Confirm your password" required="" type="password"
                            value=""></div><button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 w-full bg-[#1a4d3a] text-white hover:bg-[#1a4d3a]/90 mt-6"
                        type="submit">Create Account</button>
                </form>
                <div class="mt-4 text-center text-sm text-gray-600">Already have an account? <a href="/auth/login"
                        class="text-[#1a4d3a] hover:underline font-medium">Sign in</a></div>
            </div>
        </div>
    </div>
</body>

</html>
<?php /**PATH D:\project\resources\views/auth/register.blade.php ENDPATH**/ ?>